# 🤖 Gemini API 测试功能使用指南

## 功能概述

我们已经成功集成了 Gemini CLI API 功能到 WebContainer React 演示中，现在可以通过自然语言指令来实时修改 WebContainer 中的 React 代码。

## 🚀 功能特性

### 1. Gemini API 配置
- 支持配置 Google Gemini API Key
- 自动初始化 GeminiAdapter
- API 连接状态验证

### 2. AI 代码修改对话框
- 自然语言指令输入
- 快速示例按钮
- 实时状态反馈
- 错误处理和用户提示

### 3. 动态代码更新
- 读取当前 WebContainer 中的代码
- 调用 Gemini API 生成修改建议
- 自动将生成的代码写入文件系统
- 实时预览更新

### 4. 完善的用户体验
- 加载状态显示
- 错误处理机制
- 操作状态提示
- 示例指令快速选择

## 📋 使用步骤

### 步骤 1: 启动应用
```bash
pnpm dev
```
访问 http://localhost:3002

### 步骤 2: 启动 WebContainer
1. 点击 "WebContainer Demo" 导航
2. 点击 "启动 React 项目" 按钮
3. 等待 WebContainer 启动完成

### 步骤 3: 配置 Gemini API
1. 点击 "配置 Gemini API" 按钮
2. 输入您的 Gemini API Key
3. 点击 "配置 API" 完成设置

**获取 API Key:**
- 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
- 创建新的 API Key
- 复制并粘贴到配置界面

### 步骤 4: 使用 AI 代码修改
1. 点击 "AI 代码修改" 按钮
2. 在对话框中输入自然语言指令
3. 或点击快速示例按钮
4. 点击 "发送指令" 执行修改
5. 观察右侧预览的实时更新

## 🎯 示例指令

### 基础修改
- "将主要按钮的颜色改为红色"
- "添加一个输入框和提交按钮"
- "修改标题文字为'我的应用'"

### 组件添加
- "创建一个包含图片和描述的卡片组件"
- "添加一个数据表格显示用户信息"
- "添加一个进度条组件"

### 布局调整
- "将按钮排列改为垂直布局"
- "添加一个侧边栏导航"
- "创建两列布局"

### 样式修改
- "使用渐变背景色"
- "添加阴影效果"
- "修改字体大小和颜色"

## 🔧 技术实现

### 核心组件
- **GeminiAdapter**: Gemini API 适配器
- **AIGenerationRequest**: AI 请求接口
- **WebContainer**: 浏览器开发环境

### 工作流程
1. 用户输入自然语言指令
2. 读取当前 WebContainer 中的代码
3. 构建 AI 请求上下文
4. 调用 Gemini API 生成代码
5. 解析响应并写入文件系统
6. WebContainer 自动热重载更新预览

### 错误处理
- API 配置验证
- 网络请求错误处理
- 代码生成失败处理
- 用户友好的错误提示

## 🎨 界面特性

### 状态指示
- ✅ API 配置状态
- 🔄 处理进度显示
- ⚠️ 错误警告提示
- 📝 操作日志记录

### 交互优化
- 禁用状态管理
- 加载动画效果
- 快速示例选择
- 清空和重置功能

## 🚀 下一步扩展

### 计划功能
1. **多文件支持**: 支持修改多个组件文件
2. **代码历史**: 保存和回滚代码修改历史
3. **模板库**: 预设常用组件模板
4. **协作功能**: 多用户协作编辑

### 技术优化
1. **缓存机制**: 优化 API 调用性能
2. **增量更新**: 支持部分代码修改
3. **智能提示**: 基于上下文的指令建议
4. **代码验证**: 自动检查生成代码的正确性

## 📞 支持

如果遇到问题或有建议，请：
1. 查看浏览器控制台错误信息
2. 检查 API Key 配置是否正确
3. 确认网络连接正常
4. 查看执行日志获取详细信息

---

**注意**: 此功能需要有效的 Google Gemini API Key 才能正常工作。请确保您的 API Key 有足够的配额和权限。

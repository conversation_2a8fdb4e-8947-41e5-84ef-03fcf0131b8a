# 贡献指南

感谢您对 PixelMind AI 项目的关注！我们欢迎所有形式的贡献。

## 🤝 如何贡献

### 报告问题

如果您发现了 bug 或有功能建议：

1. 检查 [Issues](https://github.com/your-username/pixelmind-ai/issues) 确保问题未被报告
2. 创建新的 Issue，详细描述问题或建议
3. 使用合适的标签标记您的 Issue

### 提交代码

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/pixelmind-ai.git
   cd pixelmind-ai
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **安装依赖**
   ```bash
   pnpm install
   ```

4. **进行开发**
   - 遵循现有的代码风格
   - 添加必要的测试
   - 确保所有测试通过

5. **提交更改**
   ```bash
   git add .
   git commit -m "feat: 添加令人惊叹的功能"
   ```

6. **推送到分支**
   ```bash
   git push origin feature/amazing-feature
   ```

7. **创建 Pull Request**

## 📝 代码规范

### 提交信息格式

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

- `feat:` 新功能
- `fix:` 修复 bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

### 代码风格

- 使用 TypeScript
- 遵循 ESLint 和 Prettier 配置
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 优先使用中文注释和文档

## 🧪 测试

运行测试：
```bash
pnpm test
```

## 📚 开发环境

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- 推荐使用 VS Code

## 🎯 开发重点

当前项目重点关注：

1. **WebContainer 集成优化**
2. **AI 代码生成能力**
3. **可视化编辑器功能**
4. **性能优化**
5. **中文本地化**

## 📞 联系我们

如有疑问，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-username/pixelmind-ai/issues)
- 发送邮件到 [<EMAIL>]

感谢您的贡献！🎉

# 产品定位

**浏览器内的可视化 AI 前端开发环境**，允许用户通过拖拽交互和自然语言描述，生成并实时预览 React/Vue 组件代码。

# 核心功能清单

### 1. 项目初始化系统

- [ ] 用户选择技术栈（React/Vue + Vite）
- [ ] 自动创建基础项目文件（`index.html`, `package.json`等）
- [ ] 预置 UI 框架（Ant Design/Material UI）

### 2. 可视化操作引擎

- [ ] 画布选区（坐标框选生成 DOM 范围）
- [ ] 组件拖拽生成（从库中拖出预置组件）
- [ ] 实时 DOM 树渲染（同步代码变化）

### 3. AI 驱动开发系统

- [ ] 选区描述 → 组件代码（示例："在此生成带动画的登录表单"）
- [ ] 组件级 AI 编辑（示例："将此按钮颜色改为品牌色"）
- [ ] 智能定位更新（更新代码不破坏文件结构）

### 4. 开发环境模拟器

- [ ] 浏览器内启动 Vite 开发服务器（WebContainers）
- [ ] 动态路由注册（创建页面自动更新路由）
- [ ] HMR 热更新（保存即生效）

### 5. 工程化管理

- [ ] 虚拟文件系统（IndexedDB 存储代码）
- [ ] 提示词版本控制（不同 UI 库对应提示规则）
- [ ] 错误恢复系统（AI 操作失败时回滚）

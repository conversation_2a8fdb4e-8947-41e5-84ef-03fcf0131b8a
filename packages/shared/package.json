{"name": "@pixelmind/shared", "version": "0.1.0", "description": "Shared utilities and types for PixelMind AI", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.0", "@babel/types": "^7.23.0"}, "devDependencies": {"typescript": "^5.3.0", "@types/node": "^20.10.0", "@types/babel__traverse": "^7.20.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}
{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "incremental": true, "composite": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}]}
// AI prompt engineering utilities for PixelMind AI

// Export core prompt engine
export * from './core'

// Export Gemini adapter
export * from './gemini-adapter'

// Export prompt templates
export * from './templates'

// Export Ant Design specific prompts
export * from './antd'

// 新增提示词工程系统导出
export { PromptEngine } from './prompt-templates/PromptEngine'
export { TemplateLoader } from './prompt-templates/TemplateLoader'
export { PromptEngineManager } from './prompt-templates/PromptEngineManager'

export type {
  PromptTemplate,
  PromptVariable,
  PromptMetadata,
  PromptCategory,
  PromptContext,
  CompiledPrompt,
  PromptEngineConfig,
} from './prompt-templates/types'

// 便捷的工厂函数
export const createPromptEngine = (
  config?: Partial<import('./prompt-templates/types').PromptEngineConfig>
) => {
  const { PromptEngineManager } = require('./prompt-templates/PromptEngineManager')
  const manager = PromptEngineManager.getInstance()
  return manager.initialize(config).then(() => manager)
}

// 快速创建项目提示词的便捷函数
export const generateProjectPrompt = async (
  projectName: string,
  description: string,
  uiLibrary: string,
  features: string[] = [],
  animations: boolean = false
) => {
  const { PromptEngineManager } = require('./prompt-templates/PromptEngineManager')
  const manager = PromptEngineManager.getInstance()
  await manager.initialize()
  return manager.compileProjectPrompt(projectName, description, uiLibrary, features, animations)
}

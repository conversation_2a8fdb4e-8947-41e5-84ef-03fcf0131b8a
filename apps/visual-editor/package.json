{"name": "visual-editor", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "zustand": "^4.4.0", "@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.45.0", "@webcontainer/api": "^1.1.0", "@google/generative-ai": "^0.1.0", "@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.0", "@babel/types": "^7.23.0", "@pixelmind/prompt-engine": "workspace:*", "@pixelmind/shared": "workspace:*", "tailwindcss": "^3.4.0", "@tailwindcss/typography": "^0.5.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "react-router-dom": "^6.20.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.10.0", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.3.0", "vite": "^5.0.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}
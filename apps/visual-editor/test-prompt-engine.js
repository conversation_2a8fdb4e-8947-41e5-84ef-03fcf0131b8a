// 测试新的提示词工程系统
import { PromptEngineManager } from '@pixelmind/prompt-engine'

async function testPromptEngine() {
  try {
    console.log('🚀 开始测试提示词工程系统...')
    
    // 初始化引擎
    const manager = PromptEngineManager.getInstance()
    await manager.initialize({
      version: '1.0.0',
      cacheEnabled: true,
      fallbackMode: true
    })
    
    console.log('✅ 引擎初始化成功')
    
    // 获取统计信息
    const stats = manager.getStats()
    console.log('📊 引擎统计:', stats)
    
    // 生成项目提示词
    const prompt = await manager.compileProjectPrompt(
      'test-project',
      '这是一个测试项目',
      'antd',
      ['routing', 'state-management'],
      false
    )
    
    console.log('✅ 提示词生成成功')
    console.log('📝 提示词长度:', prompt.content.length)
    console.log('🔧 变量:', Object.keys(prompt.variables))
    
    // 显示提示词的前500个字符
    console.log('📄 提示词预览:')
    console.log(prompt.content.substring(0, 500) + '...')
    
    console.log('🎉 测试完成！')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

testPromptEngine()

/* Global styles for PixelMind AI */

/* CSS Reset and base styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.5;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Utility classes */
.full-height {
  height: 100vh;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-center {
  text-align: center;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Custom Ant Design overrides */
.ant-layout {
  background: #fff;
}

.ant-layout-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  background: #fafafa;
  border-right: 1px solid #f0f0f0;
}

/* Monaco Editor container */
.monaco-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* WebContainer iframe styling */
.webcontainer-preview {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Responsive design */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }
  
  html {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .ant-layout-header {
    padding: 0 12px;
  }
  
  html {
    font-size: 12px;
  }
}

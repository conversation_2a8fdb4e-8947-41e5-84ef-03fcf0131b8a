import type { CreateProjectData } from '../types/project'
import { UI_LIBRARIES } from '../utils/constants'
import { GeminiAdapter } from '@pixelmind/prompt-engine'
import type { AIGenerationRequest } from '@pixelmind/shared'

export interface GeminiProjectResponse {
  success: boolean
  message: string
  projectPath?: string
  error?: string
  generatedFiles?: Record<string, string>
}

export class GeminiService {
  private static geminiAdapter: GeminiAdapter | null = null

  /**
   * 初始化 Gemini 适配器
   */
  static initializeGemini(apiKey: string): boolean {
    try {
      this.geminiAdapter = new GeminiAdapter(apiKey)
      return this.geminiAdapter.isInitialized()
    } catch (error) {
      console.error('Gemini 初始化失败:', error)
      return false
    }
  }

  /**
   * 检查 Gemini 是否已配置
   */
  static isGeminiConfigured(): boolean {
    return this.geminiAdapter !== null && this.geminiAdapter.isInitialized()
  }

  /**
   * 获取 Gemini API Key（从 localStorage）
   */
  static getStoredApiKey(): string | null {
    try {
      return localStorage.getItem('gemini-api-key')
    } catch {
      return null
    }
  }

  /**
   * 保存 Gemini API Key 到 localStorage
   */
  static saveApiKey(apiKey: string): void {
    try {
      localStorage.setItem('gemini-api-key', apiKey)
    } catch (error) {
      console.warn('保存 API Key 失败:', error)
    }
  }

  /**
   * 生成完整的项目创建提示词
   */
  static generateProjectPrompt(projectData: CreateProjectData): string {
    const uiLibrary = UI_LIBRARIES.find(lib => lib.id === projectData.uiLibrary)

    return `# 项目创建任务

## 项目基本信息
- **项目名称**: ${projectData.name}
- **项目描述**: ${projectData.description}
- **UI 组件库**: ${uiLibrary?.name || projectData.uiLibrary}
- **技术栈**: React + Vite + TypeScript + Tailwind CSS

## 创建要求

请创建一个现代化的 React 项目，严格按照以下要求：

### 1. 技术栈配置
- **框架**: React 18+
- **构建工具**: Vite 5+
- **语言**: TypeScript 5+
- **样式**: Tailwind CSS 3+ (必须)
- **UI 组件库**: ${uiLibrary?.name || projectData.uiLibrary}

### 2. 项目结构
创建以下目录结构：
\`\`\`
${projectData.name}/
├── public/
│   ├── vite.svg
│   └── index.html
├── src/
│   ├── components/          # 可复用组件
│   │   ├── ui/             # 基础 UI 组件
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   └── Card.tsx
│   │   └── layout/         # 布局组件
│   │       ├── Header.tsx
│   │       ├── Footer.tsx
│   │       └── Layout.tsx
│   ├── pages/              # 页面组件
│   │   ├── Home.tsx
│   │   ├── About.tsx
│   │   └── NotFound.tsx
│   ├── hooks/              # 自定义 Hooks
│   │   └── useLocalStorage.ts
│   ├── utils/              # 工具函数
│   │   ├── cn.ts          # className 合并工具
│   │   └── constants.ts    # 常量定义
│   ├── types/              # TypeScript 类型定义
│   │   └── index.ts
│   ├── styles/             # 样式文件
│   │   └── globals.css
│   ├── App.tsx
│   ├── main.tsx
│   └── vite-env.d.ts
├── package.json
├── tsconfig.json
├── tsconfig.node.json
├── tailwind.config.js
├── postcss.config.js
├── vite.config.ts
├── .gitignore
├── .eslintrc.cjs
├── .prettierrc
└── README.md
\`\`\`

### 3. 依赖包配置

#### 核心依赖 (package.json dependencies)
\`\`\`json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-router-dom": "^6.8.0",
  "${uiLibrary?.packageName || 'antd'}": "latest",
  "clsx": "^2.0.0",
  "tailwind-merge": "^2.0.0"
}
\`\`\`

#### 开发依赖 (devDependencies)
\`\`\`json
{
  "@types/react": "^18.2.0",
  "@types/react-dom": "^18.2.0",
  "@vitejs/plugin-react": "^4.0.0",
  "typescript": "^5.0.0",
  "vite": "^5.0.0",
  "tailwindcss": "^3.3.0",
  "autoprefixer": "^10.4.14",
  "postcss": "^8.4.24",
  "eslint": "^8.45.0",
  "@typescript-eslint/eslint-plugin": "^6.0.0",
  "@typescript-eslint/parser": "^6.0.0",
  "eslint-plugin-react-hooks": "^4.6.0",
  "eslint-plugin-react-refresh": "^0.4.3",
  "prettier": "^3.0.0"
}
\`\`\`

### 4. 配置文件内容

#### Tailwind CSS 配置 (tailwind.config.js)
确保与 ${uiLibrary?.name || projectData.uiLibrary} 兼容，避免样式冲突。

#### Vite 配置 (vite.config.ts)
- 配置路径别名 @ 指向 src 目录
- 配置端口为 5173
- 启用热重载

#### TypeScript 配置
- 严格模式
- 路径映射支持
- 现代 ES 特性支持

### 5. 核心文件内容

#### App.tsx
创建主应用组件，包含：
- React Router 路由配置
- 布局组件集成
- 基础的导航结构
- 错误边界处理

#### main.tsx
- React 18 的 createRoot API
- 严格模式
- 样式文件导入

#### 页面组件
- **Home.tsx**: 欢迎页面，展示项目信息和 ${uiLibrary?.name || projectData.uiLibrary} 组件示例
- **About.tsx**: 关于页面，展示技术栈信息
- **NotFound.tsx**: 404 页面

### 6. 样式和主题

#### globals.css
- Tailwind CSS 基础样式导入
- 自定义 CSS 变量
- 与 ${uiLibrary?.name || projectData.uiLibrary} 的主题集成

#### 响应式设计
- 移动端优先
- 断点: sm(640px), md(768px), lg(1024px), xl(1280px)

### 7. 开发体验

#### 代码质量
- ESLint 配置
- Prettier 格式化
- TypeScript 严格检查

#### Git 配置
- .gitignore 文件
- 忽略 node_modules, dist, .env 等

#### README.md
包含：
- 项目描述
- 技术栈说明
- 安装和运行指令
- 项目结构说明
- 开发指南

### 8. 特殊要求

1. **确保所有组件都有完整的 TypeScript 类型定义**
2. **使用 Tailwind CSS 作为主要样式方案**
3. **${uiLibrary?.name || projectData.uiLibrary} 组件作为 UI 增强，不冲突**
4. **代码结构清晰，便于扩展**
5. **包含基础的错误处理**
6. **所有文件都要有适当的注释**

### 9. 安装命令
项目创建后需要执行：
\`\`\`bash
cd ${projectData.name}
npm install
${uiLibrary?.installCommand || 'npm install antd @ant-design/icons'}
npm install tailwindcss postcss autoprefixer clsx tailwind-merge
npx tailwindcss init -p
npm run dev
\`\`\`

## 验收标准
1. 项目能够成功启动 (npm run dev)
2. 所有页面正常渲染
3. 路由功能正常
4. ${uiLibrary?.name || projectData.uiLibrary} 组件正常显示
5. Tailwind CSS 样式生效
6. TypeScript 编译无错误
7. ESLint 检查通过

请严格按照以上要求创建项目，确保每个文件都有完整的内容，不要使用占位符或省略号。`
  }

  /**
   * 使用 Gemini API 创建项目文件
   */
  static async createProject(
    projectData: CreateProjectData,
    outputPath: string
  ): Promise<GeminiProjectResponse> {
    try {
      // 检查 Gemini 是否已配置
      if (!this.isGeminiConfigured()) {
        // 尝试从存储中恢复 API Key
        const storedApiKey = this.getStoredApiKey()
        if (storedApiKey) {
          const initialized = this.initializeGemini(storedApiKey)
          if (!initialized) {
            return {
              success: false,
              message: '需要配置 Gemini API',
              error: 'Gemini API 未配置或配置无效，请先配置 API Key',
            }
          }
        } else {
          return {
            success: false,
            message: '需要配置 Gemini API',
            error: 'Gemini API 未配置，请先配置 API Key',
          }
        }
      }

      console.log('开始使用 Gemini API 生成项目文件...')

      // 生成项目文件的 AI 请求
      const aiRequest: AIGenerationRequest = {
        prompt: `Create a complete React project with the following specifications:
- Project name: ${projectData.name}
- Description: ${projectData.description}
- UI Library: ${UI_LIBRARIES.find(lib => lib.id === projectData.uiLibrary)?.name || projectData.uiLibrary}
- Framework: React + Vite + TypeScript + Tailwind CSS

Generate the following files with complete content (no placeholders):
1. package.json - with all necessary dependencies
2. src/App.tsx - main application component
3. src/main.tsx - application entry point
4. src/components/ui/Button.tsx - reusable button component
5. src/components/layout/Header.tsx - header component
6. src/pages/Home.tsx - home page component
7. src/styles/globals.css - global styles with Tailwind
8. vite.config.ts - Vite configuration
9. tsconfig.json - TypeScript configuration
10. tailwind.config.js - Tailwind configuration

Each file should be production-ready with proper TypeScript types, modern React patterns, and integration with the specified UI library.`,
        context: {
          framework: 'react' as const,
          uiLibrary: (projectData.uiLibrary === 'mui' ? 'material-ui' : 'antd') as any,
          targetElement: 'project',
          existingComponents: [],
        },
        options: {
          includeTypes: true,
          includeStyles: true,
          includeTests: false,
        },
      }

      const result = await this.geminiAdapter!.processRequest(aiRequest)

      if (result.success && result.code) {
        console.log('Gemini API 生成成功')
        return {
          success: true,
          message: `项目 ${projectData.name} 文件生成成功`,
          projectPath: outputPath,
          generatedFiles: this.parseGeneratedFiles(result.code),
        }
      } else {
        console.warn('Gemini API 生成失败:', result.error)
        return {
          success: false,
          message: '项目文件生成失败',
          error: result.error || '未知错误',
        }
      }
    } catch (error) {
      console.error('Gemini API 调用失败:', error)
      return {
        success: false,
        message: '项目创建失败',
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 解析 Gemini 生成的代码，提取各个文件
   */
  private static parseGeneratedFiles(generatedCode: string): Record<string, string> {
    const files: Record<string, string> = {}

    // 使用正则表达式匹配代码块
    const filePattern =
      /```(?:typescript|javascript|json|css|html)?\s*(?:\/\/\s*(.+?)\s*)?\n([\s\S]*?)```/g
    let match

    while ((match = filePattern.exec(generatedCode)) !== null) {
      const fileName = match[1]?.trim()
      const content = match[2]?.trim()

      if (fileName && content) {
        files[fileName] = content
      }
    }

    // 如果没有找到文件，尝试其他解析方式
    if (Object.keys(files).length === 0) {
      // 简单的文件分割逻辑
      const sections = generatedCode.split(/(?=^#+ )/m)
      sections.forEach(section => {
        const lines = section.split('\n')
        const title = lines[0]?.replace(/^#+\s*/, '').trim()

        if (title && title.includes('.')) {
          const codeMatch = section.match(/```[\s\S]*?\n([\s\S]*?)```/)
          if (codeMatch) {
            files[title] = codeMatch[1].trim()
          }
        }
      })
    }

    return files
  }

  /**
   * 生成项目说明文档
   */
  static generateProjectReadme(projectData: CreateProjectData): string {
    const uiLibrary = UI_LIBRARIES.find(lib => lib.id === projectData.uiLibrary)

    return `# ${projectData.name}

${projectData.description}

## 技术栈

- **框架**: React 18+
- **构建工具**: Vite 5+
- **语言**: TypeScript 5+
- **样式**: Tailwind CSS 3+
- **UI 组件库**: ${uiLibrary?.name || projectData.uiLibrary}
- **路由**: React Router v6

## 快速开始

### 安装依赖
\`\`\`bash
npm install
\`\`\`

### 启动开发服务器
\`\`\`bash
npm run dev
\`\`\`

### 构建生产版本
\`\`\`bash
npm run build
\`\`\`

### 预览生产版本
\`\`\`bash
npm run preview
\`\`\`

## 项目结构

\`\`\`
src/
├── components/          # 可复用组件
│   ├── ui/             # 基础 UI 组件
│   └── layout/         # 布局组件
├── pages/              # 页面组件
├── hooks/              # 自定义 Hooks
├── utils/              # 工具函数
├── types/              # TypeScript 类型定义
├── styles/             # 样式文件
├── App.tsx             # 主应用组件
└── main.tsx            # 应用入口
\`\`\`

## 开发指南

### 样式系统
- 主要使用 Tailwind CSS 进行样式开发
- ${uiLibrary?.name || projectData.uiLibrary} 组件用于复杂 UI 组件
- 使用 \`cn\` 工具函数合并 className

### 组件开发
- 所有组件都使用 TypeScript
- 遵循函数式组件 + Hooks 模式
- 组件文件使用 PascalCase 命名

### 代码规范
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 提交前请确保通过所有检查

## 部署

项目构建后的文件在 \`dist\` 目录中，可以部署到任何静态文件服务器。

---

*此项目由 PixelMind AI 创建于 ${new Date().toLocaleString()}*`
  }
}

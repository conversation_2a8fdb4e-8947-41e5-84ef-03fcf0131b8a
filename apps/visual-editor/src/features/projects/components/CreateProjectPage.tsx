import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>ef<PERSON>, Loader2 } from 'lucide-react'
import { cn } from '../../../utils/cn'
import { ProjectForm } from './ProjectForm'
import { UILibrarySelector } from './UILibrarySelector'
import { ProjectLocationSelector } from './ProjectLocationSelector'
import { GeminiApiKeyModal } from './GeminiApiKeyModal'
import { ProjectManager } from '../services/ProjectManager'
import { GeminiService } from '../services/GeminiService'
import { ProjectValidator } from '../utils/validation'
import type { CreateProjectData, Project } from '../types/project'

interface CreateProjectPageProps {
  onBack: () => void
  onCreate: (project: Project) => void
}

export const CreateProjectPage: React.FC<CreateProjectPageProps> = ({ onBack, onCreate }) => {
  const [projectData, setProjectData] = useState<CreateProjectData>({
    name: '',
    description: '',
    uiLibrary: '',
  })
  const [selectedLocation, setSelectedLocation] = useState<FileSystemDirectoryHandle | null>(null)
  const [selectedLocationPath, setSelectedLocationPath] = useState<string | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isCreating, setIsCreating] = useState(false)
  const [showApiKeyModal, setShowApiKeyModal] = useState(false)

  // 检查是否已有保存的目录路径
  useEffect(() => {
    const projectManager = ProjectManager.getInstance()
    const savedPath = projectManager.getBaseDirectoryPath()
    const savedHandle = projectManager.getBaseDirectoryHandle()

    if (savedPath) {
      setSelectedLocationPath(savedPath)
      if (savedHandle) {
        setSelectedLocation(savedHandle)
      }
      console.log('已恢复保存的目录路径:', savedPath)
    }
  }, [])

  const handleLocationSelected = (dirHandle: FileSystemDirectoryHandle, path: string) => {
    setSelectedLocation(dirHandle)
    setSelectedLocationPath(path)
    setErrors(prev => ({ ...prev, location: '' }))
  }

  const handleLibrarySelect = (libraryId: string) => {
    setProjectData(prev => ({ ...prev, uiLibrary: libraryId }))
    setErrors(prev => ({ ...prev, uiLibrary: '' }))
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // 验证项目数据
    const dataValidation = ProjectValidator.validateCreateProjectData(projectData)
    if (!dataValidation.isValid) {
      if (projectData.name === '') newErrors.name = '项目名称不能为空'
      else if (!ProjectValidator.validateProjectName(projectData.name).isValid) {
        newErrors.name = ProjectValidator.validateProjectName(projectData.name).error!
      }

      if (projectData.description === '') newErrors.description = '项目描述不能为空'
      else if (!ProjectValidator.validateDescription(projectData.description).isValid) {
        newErrors.description = ProjectValidator.validateDescription(projectData.description).error!
      }

      if (projectData.uiLibrary === '') newErrors.uiLibrary = '请选择一个 UI 组件库'
    }

    // 验证存储位置
    if (!selectedLocation) {
      newErrors.location = '请选择项目存储位置'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleCreateProject = async () => {
    if (!validateForm()) return

    // 检查 Gemini API 配置
    if (!GeminiService.isGeminiConfigured()) {
      const storedKey = GeminiService.getStoredApiKey()
      if (storedKey) {
        const initialized = GeminiService.initializeGemini(storedKey)
        if (!initialized) {
          setShowApiKeyModal(true)
          return
        }
      } else {
        setShowApiKeyModal(true)
        return
      }
    }

    setIsCreating(true)
    try {
      const projectManager = ProjectManager.getInstance()

      // 检查是否有基础目录路径
      console.log('创建项目时的状态检查:')
      console.log('- selectedLocationPath:', selectedLocationPath)
      console.log('- selectedLocation:', selectedLocation)
      console.log('- projectData:', projectData)

      if (!selectedLocationPath) {
        console.error('selectedLocationPath 为空，无法创建项目')
        alert('请先选择项目存储目录')
        setIsCreating(false)
        return
      }

      // 如果没有文件系统访问权限，尝试重新获取
      if (!projectManager.hasFileSystemAccess()) {
        try {
          const result = await projectManager.setBaseDirectory(true) // 强制重新选择
          if (!result.success) {
            alert('需要重新授权访问目录权限，请重新选择目录')
            setIsCreating(false)
            return
          }
        } catch (error) {
          console.error('重新获取目录权限失败:', error)
          alert('无法访问目录，请重新选择项目存储位置')
          setIsCreating(false)
          return
        }
      }

      console.log('开始创建项目...')
      const project = await projectManager.createProject(projectData)

      console.log('项目创建成功:', project)
      alert(
        `项目 "${project.name}" 创建成功！\n\n项目已保存到本地，并通过 AI 生成了完整的项目结构。`
      )

      // 调用回调函数，通常会跳转到项目列表页面
      onCreate(project)
    } catch (error) {
      console.error('创建项目失败:', error)

      // 检查是否是 API Key 相关错误
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      if (errorMessage.includes('API') || errorMessage.includes('Gemini')) {
        alert(`创建项目失败: ${errorMessage}\n\n请检查 Gemini API Key 配置是否正确。`)
        setShowApiKeyModal(true)
      } else {
        alert(`创建项目失败: ${errorMessage}`)
      }
    } finally {
      setIsCreating(false)
    }
  }

  const handleApiKeyConfigured = () => {
    console.log('Gemini API Key 配置成功')
    // 配置成功后可以继续创建项目
  }

  return (
    <div className="h-full bg-white dark:bg-gray-900 text-gray-900 dark:text-white overflow-auto">
      {/* 头部 */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            disabled={isCreating}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold">创建新项目</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">配置你的 React + Vite 项目</p>
          </div>
        </div>
      </div>

      <div className="p-6 max-w-4xl mx-auto space-y-8">
        {/* 项目基本信息 */}
        <div>
          <h2 className="text-lg font-semibold mb-6">项目信息</h2>
          <ProjectForm projectData={projectData} onDataChange={setProjectData} errors={errors} />
        </div>

        {/* UI 组件库选择 */}
        <div>
          <UILibrarySelector
            selectedLibrary={projectData.uiLibrary}
            onLibrarySelect={handleLibrarySelect}
            error={errors.uiLibrary}
          />
        </div>

        {/* 项目存储位置 */}
        <div>
          <ProjectLocationSelector
            onLocationSelected={handleLocationSelected}
            selectedLocation={selectedLocationPath}
            error={errors.location}
          />
        </div>
        {/* 创建按钮 */}
        <div className="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onBack}
            disabled={isCreating}
            className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={handleCreateProject}
            disabled={
              isCreating ||
              !projectData.name ||
              !projectData.description ||
              !projectData.uiLibrary ||
              !selectedLocationPath
            }
            className={cn(
              'px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2',
              isCreating ||
                !projectData.name ||
                !projectData.description ||
                !projectData.uiLibrary ||
                !selectedLocationPath
                ? 'bg-gray-400 dark:bg-gray-600 text-gray-200 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            )}
          >
            {isCreating && <Loader2 className="w-4 h-4 animate-spin" />}
            {isCreating ? '创建中...' : '创建项目'}
          </button>
        </div>
      </div>

      {/* Gemini API Key 配置模态框 */}
      <GeminiApiKeyModal
        open={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        onConfigured={handleApiKeyConfigured}
      />
    </div>
  )
}

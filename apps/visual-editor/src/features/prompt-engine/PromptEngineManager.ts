import { PromptEngine } from './core/PromptEngine'
import { TemplateLoader } from './loaders/TemplateLoader'
import type { 
  PromptTemplate, 
  PromptContext, 
  CompiledPrompt, 
  PromptEngineConfig 
} from './types'

/**
 * 提示词工程管理器
 * 统一管理提示词引擎的初始化、配置和使用
 */
export class PromptEngineManager {
  private static instance: PromptEngineManager | null = null
  private engine: PromptEngine
  private loader: TemplateLoader
  private initialized: boolean = false

  private constructor() {
    this.engine = new PromptEngine()
    this.loader = new TemplateLoader()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PromptEngineManager {
    if (!PromptEngineManager.instance) {
      PromptEngineManager.instance = new PromptEngineManager()
    }
    return PromptEngineManager.instance
  }

  /**
   * 初始化提示词引擎
   */
  async initialize(config: Partial<PromptEngineConfig> = {}): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 重新创建引擎实例以应用配置
      this.engine = new PromptEngine(config)
      
      // 配置模板加载器
      if (config.baseUrl) {
        this.loader.setBaseUrl(config.baseUrl)
      }
      if (config.apiKey) {
        this.loader.setApiKey(config.apiKey)
      }

      // 加载内置模板
      await this.loadBuiltinTemplates()

      // 如果配置了远程 URL，尝试加载远程模板
      if (config.baseUrl) {
        try {
          await this.loadRemoteTemplates()
        } catch (error) {
          console.warn('Failed to load remote templates:', error)
          if (!config.fallbackMode) {
            throw error
          }
        }
      }

      this.initialized = true
      console.log('PromptEngine initialized successfully')
    } catch (error) {
      console.error('Failed to initialize PromptEngine:', error)
      throw error
    }
  }

  /**
   * 加载内置模板
   */
  private async loadBuiltinTemplates(): Promise<void> {
    const builtinTemplates = [
      '/src/features/prompt-engine/templates/project-creation/react-vite-base.json'
      // 可以添加更多内置模板
    ]

    try {
      const templates = await this.loader.loadLocalTemplates(builtinTemplates)
      this.engine.registerTemplates(templates)
      console.log(`Loaded ${templates.length} builtin templates`)
    } catch (error) {
      console.error('Failed to load builtin templates:', error)
      // 内置模板加载失败时使用硬编码的后备模板
      this.loadFallbackTemplates()
    }
  }

  /**
   * 加载远程模板
   */
  private async loadRemoteTemplates(): Promise<void> {
    try {
      const templateList = await this.loader.getRemoteTemplateList()
      console.log(`Found ${templateList.length} remote templates`)

      for (const templateInfo of templateList) {
        try {
          const template = await this.loader.loadRemoteTemplate(templateInfo.id)
          this.engine.registerTemplate(template)
        } catch (error) {
          console.warn(`Failed to load remote template ${templateInfo.id}:`, error)
        }
      }
    } catch (error) {
      console.error('Failed to load remote templates:', error)
      throw error
    }
  }

  /**
   * 加载后备模板（硬编码）
   */
  private loadFallbackTemplates(): void {
    const fallbackTemplate: PromptTemplate = {
      id: 'react-vite-fallback',
      name: 'React + Vite 后备模板',
      description: '当其他模板加载失败时使用的后备模板',
      version: '1.0.0',
      category: 'project-creation',
      tags: ['react', 'vite', 'fallback'],
      variables: [
        {
          name: 'projectName',
          type: 'string',
          required: true,
          description: '项目名称'
        },
        {
          name: 'projectDescription',
          type: 'string',
          required: true,
          description: '项目描述'
        },
        {
          name: 'uiLibrary',
          type: 'string',
          required: true,
          description: 'UI 组件库'
        }
      ],
      content: `# 项目创建任务

## 项目基本信息
- **项目名称**: {{projectName}}
- **项目描述**: {{projectDescription}}
- **UI 组件库**: {{uiLibraryName}}
- **技术栈**: React + Vite + TypeScript + Tailwind CSS

请创建一个基础的 React + Vite 项目，包含基本的项目结构和配置文件。`,
      metadata: {
        author: 'PixelMind AI',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        license: 'proprietary',
        compatibility: ['react@18+'],
        dependencies: []
      }
    }

    this.engine.registerTemplate(fallbackTemplate)
    console.log('Loaded fallback template')
  }

  /**
   * 编译项目创建提示词
   */
  async compileProjectPrompt(
    projectName: string,
    projectDescription: string,
    uiLibrary: string,
    features: string[] = [],
    animations: boolean = false
  ): Promise<CompiledPrompt> {
    if (!this.initialized) {
      await this.initialize()
    }

    const context: PromptContext = {
      projectType: 'react-vite',
      uiLibrary,
      framework: 'react',
      features,
      customVariables: {}
    }

    const variables = {
      projectName,
      projectDescription,
      uiLibrary,
      features,
      animations
    }

    return await this.engine.compilePrompt('react-vite-base', context, variables)
  }

  /**
   * 获取可用模板列表
   */
  getAvailableTemplates(): PromptTemplate[] {
    return this.engine.getAllTemplates()
  }

  /**
   * 获取特定分类的模板
   */
  getTemplatesByCategory(category: string): PromptTemplate[] {
    return this.engine.getTemplatesByCategory(category)
  }

  /**
   * 获取引擎统计信息
   */
  getStats() {
    return this.engine.getStats()
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.engine.clearCache()
  }

  /**
   * 重置引擎（用于测试）
   */
  reset(): void {
    this.initialized = false
    this.engine = new PromptEngine()
    this.loader = new TemplateLoader()
  }
}

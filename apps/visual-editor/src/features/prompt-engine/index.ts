// 提示词工程模块导出

export { PromptEngine } from './core/PromptEngine'
export { TemplateLoader } from './loaders/TemplateLoader'
export { PromptEngineManager } from './PromptEngineManager'

export type {
  PromptTemplate,
  PromptVariable,
  PromptMetadata,
  PromptCategory,
  PromptContext,
  CompiledPrompt,
  PromptEngineConfig
} from './types'

// 便捷的工厂函数
export const createPromptEngine = (config?: Partial<import('./types').PromptEngineConfig>) => {
  const manager = PromptEngineManager.getInstance()
  return manager.initialize(config).then(() => manager)
}

// 快速创建项目提示词的便捷函数
export const generateProjectPrompt = async (
  projectName: string,
  description: string,
  uiLibrary: string,
  features: string[] = [],
  animations: boolean = false
) => {
  const manager = PromptEngineManager.getInstance()
  await manager.initialize()
  return manager.compileProjectPrompt(projectName, description, uiLibrary, features, animations)
}

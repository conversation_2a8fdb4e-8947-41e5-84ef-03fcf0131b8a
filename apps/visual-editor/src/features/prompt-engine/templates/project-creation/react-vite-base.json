{"id": "react-vite-base", "name": "React + Vite 基础项目", "description": "创建基于 React + Vite + TypeScript 的现代化项目", "version": "1.0.0", "category": "project-creation", "tags": ["react", "vite", "typescript", "tailwind"], "variables": [{"name": "projectName", "type": "string", "required": true, "description": "项目名称"}, {"name": "projectDescription", "type": "string", "required": true, "description": "项目描述"}, {"name": "uiLibrary", "type": "string", "required": true, "description": "UI 组件库", "options": ["antd", "mui", "chakra", "mantine", "nextui", "arco"]}, {"name": "features", "type": "array", "required": false, "description": "项目特性", "defaultValue": ["routing", "state-management", "api-client"]}, {"name": "animations", "type": "boolean", "required": false, "description": "是否包含动画效果", "defaultValue": false}], "content": "# 项目创建任务\n\n## 项目基本信息\n- **项目名称**: {{projectName}}\n- **项目描述**: {{projectDescription}}\n- **UI 组件库**: {{uiLibraryName}}\n- **技术栈**: React + Vite + TypeScript + Tailwind CSS\n{{#if animations}}\n- **动画库**: Framer Motion\n{{/if}}\n\n## 创建要求\n\n请创建一个现代化的 React 项目，严格按照以下要求：\n\n### 1. 技术栈配置\n- **框架**: React 18+\n- **构建工具**: Vite 5+\n- **语言**: TypeScript 5+\n- **样式**: Tailwind CSS 3+ (必须)\n- **UI 组件库**: {{uiLibraryName}}\n{{#if animations}}\n- **动画**: Framer Motion\n{{/if}}\n\n### 2. 项目结构\n创建以下目录结构：\n```\n{{projectName}}/\n├── public/\n│   ├── vite.svg\n│   └── index.html\n├── src/\n│   ├── components/          # 可复用组件\n│   │   ├── ui/             # 基础 UI 组件\n│   │   │   ├── Button.tsx\n│   │   │   ├── Input.tsx\n│   │   │   └── Card.tsx\n│   │   └── layout/         # 布局组件\n│   │       ├── Header.tsx\n│   │       ├── Footer.tsx\n│   │       └── Layout.tsx\n│   ├── pages/              # 页面组件\n│   │   ├── Home.tsx\n│   │   ├── About.tsx\n│   │   └── NotFound.tsx\n{{#if features.includes 'routing'}}\n│   ├── router/             # 路由配置\n│   │   └── index.tsx\n{{/if}}\n{{#if features.includes 'state-management'}}\n│   ├── store/              # 状态管理\n│   │   └── index.ts\n{{/if}}\n│   ├── hooks/              # 自定义 Hooks\n│   │   └── useLocalStorage.ts\n│   ├── utils/              # 工具函数\n│   │   ├── cn.ts          # className 合并工具\n│   │   └── constants.ts    # 常量定义\n│   ├── types/              # TypeScript 类型定义\n│   │   └── index.ts\n│   ├── styles/             # 样式文件\n│   │   └── globals.css\n{{#if animations}}\n│   ├── animations/         # 动画配置\n│   │   └── variants.ts\n{{/if}}\n│   ├── App.tsx\n│   ├── main.tsx\n│   └── vite-env.d.ts\n├── package.json\n├── tsconfig.json\n├── tsconfig.node.json\n├── tailwind.config.js\n├── postcss.config.js\n├── vite.config.ts\n├── .gitignore\n├── .eslintrc.cjs\n├── .prettierrc\n└── README.md\n```\n\n### 3. 依赖包配置\n\n#### 核心依赖 (package.json dependencies)\n```json\n{\n  \"react\": \"^18.2.0\",\n  \"react-dom\": \"^18.2.0\",\n{{#if features.includes 'routing'}}\n  \"react-router-dom\": \"^6.8.0\",\n{{/if}}\n  \"{{uiLibraryPackage}}\": \"latest\",\n  \"clsx\": \"^2.0.0\",\n  \"tailwind-merge\": \"^2.0.0\"\n{{#if animations}},\n  \"framer-motion\": \"^10.0.0\"\n{{/if}}\n{{#if features.includes 'state-management'}},\n  \"zustand\": \"^4.4.0\"\n{{/if}}\n}\n```\n\n### 4. 特殊要求\n\n1. **确保所有组件都有完整的 TypeScript 类型定义**\n2. **使用 Tailwind CSS 作为主要样式方案**\n3. **{{uiLibraryName}} 组件作为 UI 增强，不冲突**\n4. **代码结构清晰，便于扩展**\n5. **包含基础的错误处理**\n6. **所有文件都要有适当的注释**\n{{#if animations}}\n7. **使用 Framer Motion 添加页面切换和组件动画**\n{{/if}}\n\n## 验收标准\n1. 项目能够成功启动 (npm run dev)\n2. 所有页面正常渲染\n{{#if features.includes 'routing'}}\n3. 路由功能正常\n{{/if}}\n4. {{uiLibraryName}} 组件正常显示\n5. Tailwind CSS 样式生效\n6. TypeScript 编译无错误\n7. ESLint 检查通过\n{{#if animations}}\n8. 动画效果正常工作\n{{/if}}\n\n请严格按照以上要求创建项目，确保每个文件都有完整的内容，不要使用占位符或省略号。", "metadata": {"author": "PixelMind AI", "createdAt": "2024-01-20T00:00:00Z", "updatedAt": "2024-01-20T00:00:00Z", "license": "proprietary", "compatibility": ["react@18+", "vite@5+", "typescript@5+"], "dependencies": []}}
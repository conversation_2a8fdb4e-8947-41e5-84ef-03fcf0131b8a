import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { WorkspaceHeader } from './components/WorkspaceHeader'
import { ProjectSidebarNew as ProjectSidebar } from './components/ProjectSidebarNew'
import { PreviewPanelNew as PreviewPanel } from './components/PreviewPanelNew'
import { AIAssistantSimple } from './components/AIAssistantSimple'
import { ProjectManager } from '../projects/services/ProjectManager'
import type { Project } from '../projects/types/project'
import { cn } from '../../utils/cn'

/**
 * 项目工作台页面
 * 左侧：页面和组件管理，右侧：预览区域+AI对话
 */
export const WorkspacePage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [aiPanelVisible, setAiPanelVisible] = useState(true)
  const [currentProject, setCurrentProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)

  // 获取项目信息
  useEffect(() => {
    const loadProject = async () => {
      if (!projectId) {
        setLoading(false)
        return
      }

      try {
        const projectManager = ProjectManager.getInstance()
        const projects = await projectManager.getProjects()
        const project = projects.find(p => p.id === projectId)

        if (project) {
          setCurrentProject(project)
        } else {
          console.error('项目未找到:', projectId)
        }
      } catch (error) {
        console.error('加载项目失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProject()
  }, [projectId])

  // 如果项目ID存在但项目未找到，显示错误信息
  if (projectId && !loading && !currentProject) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
            项目未找到
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mb-4">项目 ID: {projectId}</p>
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            返回上一页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('h-screen flex flex-col', 'bg-gray-50 dark:bg-gray-950')}>
      {/* 工作台头部操作栏 */}
      <WorkspaceHeader
        onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
        onToggleAI={() => setAiPanelVisible(!aiPanelVisible)}
        sidebarCollapsed={sidebarCollapsed}
        aiPanelVisible={aiPanelVisible}
        projectName={currentProject?.name}
        projectId={currentProject?.id}
      />

      {/* 主要工作区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧边栏 - 页面和组件管理 */}
        <div
          className={cn(
            'transition-all duration-300',
            sidebarCollapsed ? 'w-0' : 'w-80',
            'border-r border-gray-200 dark:border-gray-700',
            'bg-white dark:bg-gray-900'
          )}
        >
          {!sidebarCollapsed && <ProjectSidebar />}
        </div>

        {/* 右侧区域 - 预览和AI对话 */}
        <div className="flex-1 flex">
          {/* 预览区域 */}
          <div className={cn('transition-all duration-300', aiPanelVisible ? 'flex-1' : 'w-full')}>
            <PreviewPanel />
          </div>

          {/* AI 对话区域 */}
          {aiPanelVisible && (
            <div
              className={cn(
                'w-96 border-l border-gray-200 dark:border-gray-700 border-l-fix',
                'bg-white dark:bg-gray-900'
              )}
            >
              <AIAssistantSimple />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

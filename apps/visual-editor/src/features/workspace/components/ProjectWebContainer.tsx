import React, { useState, useEffect, useRef } from 'react'
import { useParams } from 'react-router-dom'
import { Button, Space, Typography, Spin, Alert, Card } from 'antd'
import { Play, Square, RotateCcw, ExternalLink, Terminal, Folder } from 'lucide-react'
import { WebContainerService } from '../../../core/webcontainer/WebContainerService'
import { ProjectManager } from '../../projects/services/ProjectManager'
import type { Project } from '../../projects/types/project'
import type {
  WebContainerFileTree,
  WebContainerFile,
  WebContainerDirectory,
} from '@pixelmind/shared'

const { Title, Text } = Typography

interface ProjectWebContainerProps {
  project?: Project
}

/**
 * 项目 WebContainer 管理组件
 * 负责启动、管理和预览用户创建的项目
 */
export const ProjectWebContainer: React.FC<ProjectWebContainerProps> = ({ project }) => {
  const { projectId } = useParams<{ projectId: string }>()
  const [isLoading, setIsLoading] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [currentProject, setCurrentProject] = useState<Project | null>(project || null)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const webContainerService = useRef<WebContainerService | null>(null)

  // 转换文件格式为 WebContainer 格式
  const convertToWebContainerFormat = (files: Record<string, string>): WebContainerFileTree => {
    const fileTree: WebContainerFileTree = {}

    for (const [filePath, content] of Object.entries(files)) {
      const pathParts = filePath.split('/')
      let current = fileTree

      // 处理嵌套目录
      for (let i = 0; i < pathParts.length - 1; i++) {
        const dirName = pathParts[i]
        if (!current[dirName]) {
          current[dirName] = { directory: {} } as WebContainerDirectory
        }
        current = (current[dirName] as WebContainerDirectory).directory
      }

      // 添加文件
      const fileName = pathParts[pathParts.length - 1]
      current[fileName] = {
        file: {
          contents: content,
        },
      } as WebContainerFile
    }

    return fileTree
  }

  // 加载项目信息
  useEffect(() => {
    if (!currentProject && projectId) {
      loadProject()
    }
  }, [projectId, currentProject])

  const loadProject = async () => {
    try {
      const projectManager = ProjectManager.getInstance()
      const projects = await projectManager.getProjects()
      const foundProject = projects.find(p => p.id === projectId)

      if (foundProject) {
        setCurrentProject(foundProject)
      } else {
        setError('项目未找到')
      }
    } catch (err) {
      console.error('加载项目失败:', err)
      setError('加载项目失败')
    }
  }

  // 启动项目
  const startProject = async () => {
    if (!currentProject) {
      setError('没有选择项目')
      return
    }

    setIsLoading(true)
    setError(null)
    setLogs([])

    try {
      // 初始化 WebContainer 服务
      if (!webContainerService.current) {
        webContainerService.current = new WebContainerService()
      }

      addLog('正在启动 WebContainer...')
      await webContainerService.current.initialize()

      addLog('正在加载项目文件...')

      // 从文件系统读取项目文件
      const projectManager = ProjectManager.getInstance()
      const projectFiles = await projectManager.getProjectFiles(currentProject.id)

      if (!projectFiles || Object.keys(projectFiles).length === 0) {
        throw new Error('项目文件为空或无法读取')
      }

      addLog(`已加载 ${Object.keys(projectFiles).length} 个文件`)

      // 转换文件格式并挂载到 WebContainer
      const webContainerFiles = convertToWebContainerFormat(projectFiles)
      await webContainerService.current.mountFiles(webContainerFiles)
      addLog('文件挂载完成')

      // 安装依赖
      addLog('正在安装依赖 (pnpm install)...')
      await webContainerService.current.installDependencies()
      addLog('依赖安装完成')

      // 启动开发服务器
      addLog('正在启动开发服务器...')
      const devServerProcess = await webContainerService.current.startDevServer()
      addLog('开发服务器进程已启动')

      // 等待一段时间让服务器完全启动
      addLog('等待服务器完全启动...')
      await new Promise(resolve => setTimeout(resolve, 8000))

      // 获取服务器 URL
      addLog('获取服务器 URL...')
      const url = await webContainerService.current.getServerUrl()

      if (url) {
        setPreviewUrl(url)
        setIsRunning(true)
        addLog(`开发服务器已启动: ${url}`)
      } else {
        throw new Error('无法获取预览 URL')
      }
    } catch (err) {
      console.error('启动项目失败:', err)
      setError(err instanceof Error ? err.message : '启动失败')
      addLog(`错误: ${err instanceof Error ? err.message : '启动失败'}`)
    } finally {
      setIsLoading(false)
    }
  }

  // 停止项目
  const stopProject = async () => {
    if (webContainerService.current) {
      try {
        await webContainerService.current.stop()
        setIsRunning(false)
        setPreviewUrl(null)
        addLog('项目已停止')
      } catch (err) {
        console.error('停止项目失败:', err)
        setError('停止项目失败')
      }
    }
  }

  // 重启项目
  const restartProject = async () => {
    await stopProject()
    setTimeout(() => {
      startProject()
    }, 1000)
  }

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  // 在浏览器中打开
  const openInBrowser = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank')
    }
  }

  if (!currentProject) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <Folder className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <Title level={4} type="secondary">
            {projectId ? '加载项目中...' : '请选择一个项目'}
          </Title>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 控制栏 */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <Title level={4} className="!mb-1">
              {currentProject.name}
            </Title>
            <Text type="secondary">{currentProject.description}</Text>
          </div>

          <Space>
            {!isRunning ? (
              <Button
                type="primary"
                icon={<Play className="w-4 h-4" />}
                onClick={startProject}
                loading={isLoading}
                disabled={isLoading}
              >
                启动项目
              </Button>
            ) : (
              <>
                <Button
                  icon={<Square className="w-4 h-4" />}
                  onClick={stopProject}
                  disabled={isLoading}
                >
                  停止
                </Button>
                <Button
                  icon={<RotateCcw className="w-4 h-4" />}
                  onClick={restartProject}
                  disabled={isLoading}
                >
                  重启
                </Button>
                {previewUrl && (
                  <Button icon={<ExternalLink className="w-4 h-4" />} onClick={openInBrowser}>
                    在浏览器中打开
                  </Button>
                )}
              </>
            )}
          </Space>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 预览区域 */}
        <div className="flex-1 flex flex-col">
          {error && (
            <Alert
              message="错误"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              className="m-4"
            />
          )}

          {isLoading && (
            <div className="flex-1 flex items-center justify-center">
              <Spin size="large" />
              <Text className="ml-4">正在启动项目...</Text>
            </div>
          )}

          {previewUrl && !isLoading && (
            <div className="flex-1 p-4">
              <iframe
                ref={iframeRef}
                src={previewUrl}
                className="w-full h-full border border-gray-200 dark:border-gray-700 rounded-lg"
                title={`${currentProject.name} 预览`}
              />
            </div>
          )}

          {!previewUrl && !isLoading && !error && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Terminal className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <Title level={4} type="secondary">
                  点击"启动项目"开始开发
                </Title>
                <Text type="secondary">项目将在 WebContainer 中运行，支持热重载和实时预览</Text>
              </div>
            </div>
          )}
        </div>

        {/* 日志面板 */}
        {logs.length > 0 && (
          <div className="w-80 border-l border-gray-200 dark:border-gray-700">
            <Card
              title={
                <div className="flex items-center gap-2">
                  <Terminal className="w-4 h-4" />
                  <span>运行日志</span>
                </div>
              }
              size="small"
              className="h-full"
              bodyStyle={{ padding: 0, height: 'calc(100% - 57px)', overflow: 'hidden' }}
            >
              <div className="h-full overflow-y-auto p-3 bg-gray-50 dark:bg-gray-900 font-mono text-xs">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1 text-gray-700 dark:text-gray-300">
                    {log}
                  </div>
                ))}
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

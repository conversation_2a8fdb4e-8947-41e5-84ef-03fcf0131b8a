import React, { useState } from 'react'
import { Tabs, Typography } from 'antd'
import { Monitor, Play } from 'lucide-react'
import { ProjectWebContainer } from './ProjectWebContainer'
import { useWorkspaceStore } from '../store/useWorkspaceStore'

const { Text } = Typography

/**
 * 新的预览面板组件
 * 支持设计预览和项目运行两种模式
 */
export const PreviewPanelNew: React.FC = () => {
  const { selectedPageId, selectedComponentId } = useWorkspaceStore()
  const [activeTab, setActiveTab] = useState('project') // 默认显示项目运行

  // 模拟页面内容
  const renderDesignPreview = () => {
    // 根据选中的页面ID渲染不同内容
    switch (selectedPageId) {
      case 'page-1': // 首页
        return (
          <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-full">
            <div className="max-w-4xl mx-auto">
              {/* Header */}
              <header className="flex items-center justify-between mb-8 p-4 bg-white rounded-lg shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg"></div>
                  <span className="font-bold text-xl">PixelMind</span>
                </div>
                <nav className="flex gap-6">
                  <a href="#" className="text-gray-600 hover:text-blue-500">
                    首页
                  </a>
                  <a href="#" className="text-gray-600 hover:text-blue-500">
                    产品
                  </a>
                  <a href="#" className="text-gray-600 hover:text-blue-500">
                    关于
                  </a>
                </nav>
              </header>

              {/* Hero Section */}
              <section className="text-center mb-12 p-8 bg-white rounded-xl shadow-sm">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">🚀 欢迎来到 PixelMind AI</h1>
                <p className="text-xl text-gray-600 mb-6">智能化的可视化网站构建平台</p>
                <div className="flex gap-4 justify-center">
                  <button className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    开始创建
                  </button>
                  <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                    了解更多
                  </button>
                </div>
              </section>

              {/* Features */}
              <section className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="p-6 bg-white rounded-lg shadow-sm">
                  <div className="text-3xl mb-4">🎨</div>
                  <h3 className="text-xl font-semibold mb-2">可视化设计</h3>
                  <p className="text-gray-600">拖拽式界面设计，无需编程基础</p>
                </div>
                <div className="p-6 bg-white rounded-lg shadow-sm">
                  <div className="text-3xl mb-4">🤖</div>
                  <h3 className="text-xl font-semibold mb-2">AI 智能助手</h3>
                  <p className="text-gray-600">智能代码生成和优化建议</p>
                </div>
                <div className="p-6 bg-white rounded-lg shadow-sm">
                  <div className="text-3xl mb-4">⚡</div>
                  <h3 className="text-xl font-semibold mb-2">快速部署</h3>
                  <p className="text-gray-600">一键部署到云端，即时上线</p>
                </div>
              </section>
            </div>
          </div>
        )
      case 'page-2': // 产品列表
        return (
          <div className="p-6 bg-gray-50 min-h-full">
            <div className="max-w-6xl mx-auto">
              <h1 className="text-3xl font-bold text-gray-900 mb-8">产品列表</h1>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3, 4, 5, 6].map(i => (
                  <div key={i} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="w-full h-40 bg-gray-200 rounded-lg mb-4"></div>
                    <h3 className="text-lg font-semibold mb-2">产品 {i}</h3>
                    <p className="text-gray-600 mb-4">这是产品 {i} 的描述信息</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-bold text-blue-500">¥{i * 100}</span>
                      <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        查看详情
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className="flex items-center justify-center h-full bg-gray-50">
            <div className="text-center">
              <div className="text-6xl mb-4">🎨</div>
              <h2 className="text-2xl font-semibold text-gray-700 mb-2">选择页面预览</h2>
              <p className="text-gray-500">从左侧选择页面或组件来查看预览</p>
            </div>
          </div>
        )
    }
  }

  const tabItems = [
    {
      key: 'project',
      label: (
        <span className="flex items-center gap-2">
          <Play className="w-4 h-4" />
          项目运行
        </span>
      ),
      children: <ProjectWebContainer />,
    },
    {
      key: 'preview',
      label: (
        <span className="flex items-center gap-2">
          <Monitor className="w-4 h-4" />
          设计预览
        </span>
      ),
      children: (
        <div className="h-full overflow-auto">
          {renderDesignPreview()}
        </div>
      ),
    },
  ]

  return (
    <div className="h-full flex flex-col">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="small"
        className="h-full flex flex-col"
        tabBarStyle={{ 
          margin: 0, 
          padding: '0 16px',
          borderBottom: '1px solid #e5e7eb'
        }}
        style={{ height: '100%' }}
      />
    </div>
  )
}

import React, { useEffect, useRef, useState } from 'react'
import { WebContainer } from '@webcontainer/api'
import { <PERSON>ton, Card, Typography, Space, Spin, Alert } from 'antd'
import { Play, Square, ExternalLink } from 'lucide-react'

const { Title, Text } = Typography

// 简单的 React + Vite 项目文件
const files = {
  'package.json': {
    file: {
      contents: JSON.stringify({
        name: 'react-webcontainer-demo',
        version: '1.0.0',
        type: 'module',
        scripts: {
          dev: 'vite',
          build: 'vite build',
          preview: 'vite preview'
        },
        dependencies: {
          react: '^18.2.0',
          'react-dom': '^18.2.0'
        },
        devDependencies: {
          '@types/react': '^18.2.0',
          '@types/react-dom': '^18.2.0',
          '@vitejs/plugin-react': '^4.0.0',
          vite: '^4.4.0'
        }
      }, null, 2)
    }
  },
  'index.html': {
    file: {
      contents: `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React WebContainer Demo</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>`
    }
  },
  'vite.config.js': {
    file: {
      contents: `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
})`
    }
  },
  'src': {
    directory: {
      'main.jsx': {
        file: {
          contents: `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`
        }
      },
      'App.jsx': {
        file: {
          contents: `import React, { useState } from 'react'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div style={{ padding: '2rem', textAlign: 'center' }}>
      <h1>🎉 WebContainer React Demo</h1>
      <p>这是一个运行在浏览器中的真实 React 应用！</p>
      
      <div style={{ margin: '2rem 0' }}>
        <button 
          onClick={() => setCount(count + 1)}
          style={{
            padding: '0.5rem 1rem',
            fontSize: '1rem',
            backgroundColor: '#007acc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          点击次数: {count}
        </button>
      </div>
      
      <p style={{ color: '#666' }}>
        这个应用完全运行在 WebContainer 中，包括：
      </p>
      <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
        <li>✅ React 18</li>
        <li>✅ Vite 开发服务器</li>
        <li>✅ 热重载</li>
        <li>✅ npm 包管理</li>
        <li>✅ 完整的 Node.js 环境</li>
      </ul>
    </div>
  )
}

export default App`
        }
      }
    }
  }
}

export const WebContainerDemo: React.FC = () => {
  const [webcontainer, setWebcontainer] = useState<WebContainer | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`])
  }

  const startDemo = async () => {
    setIsLoading(true)
    setError(null)
    setLogs([])

    try {
      addLog('🚀 启动 WebContainer...')
      
      // Boot WebContainer
      const webcontainerInstance = await WebContainer.boot()
      setWebcontainer(webcontainerInstance)
      addLog('✅ WebContainer 启动成功')

      // Mount files
      addLog('📁 挂载项目文件...')
      await webcontainerInstance.mount(files)
      addLog('✅ 文件挂载完成')

      // Listen for server-ready event
      webcontainerInstance.on('server-ready', (port, url) => {
        addLog(`🎯 服务器就绪: ${url}`)
        setPreviewUrl(url)
        setIsRunning(true)
      })

      // Install dependencies
      addLog('📦 安装依赖...')
      const installProcess = await webcontainerInstance.spawn('npm', ['install'])
      const installExitCode = await installProcess.exit
      
      if (installExitCode !== 0) {
        throw new Error('依赖安装失败')
      }
      addLog('✅ 依赖安装完成')

      // Start dev server
      addLog('🔥 启动开发服务器...')
      await webcontainerInstance.spawn('npm', ['run', 'dev'])
      addLog('✅ 开发服务器已启动')

    } catch (err) {
      console.error('Demo 启动失败:', err)
      setError(err instanceof Error ? err.message : '启动失败')
      addLog(`❌ 错误: ${err instanceof Error ? err.message : '启动失败'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const stopDemo = async () => {
    if (webcontainer) {
      await webcontainer.teardown()
      setWebcontainer(null)
      setIsRunning(false)
      setPreviewUrl(null)
      addLog('🛑 WebContainer 已停止')
    }
  }

  const openInBrowser = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank')
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* 控制面板 */}
      <Card className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <Title level={4} className="mb-2">WebContainer React 演示</Title>
            <Text type="secondary">
              这将启动一个真实的 React + Vite 项目，完全运行在您的浏览器中
            </Text>
          </div>
          <Space>
            {!isRunning ? (
              <Button 
                type="primary" 
                icon={<Play className="w-4 h-4" />}
                onClick={startDemo}
                loading={isLoading}
              >
                启动演示
              </Button>
            ) : (
              <>
                <Button 
                  icon={<Square className="w-4 h-4" />}
                  onClick={stopDemo}
                >
                  停止
                </Button>
                {previewUrl && (
                  <Button 
                    icon={<ExternalLink className="w-4 h-4" />} 
                    onClick={openInBrowser}
                  >
                    在新窗口打开
                  </Button>
                )}
              </>
            )}
          </Space>
        </div>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="mb-4"
        />
      )}

      {/* 主要内容 */}
      <div className="flex-1 flex gap-4">
        {/* 预览区域 */}
        <div className="flex-1 flex flex-col">
          {isLoading && (
            <div className="flex-1 flex items-center justify-center">
              <Spin size="large" />
              <Text className="ml-4">正在启动 WebContainer...</Text>
            </div>
          )}

          {previewUrl && !isLoading && (
            <iframe
              ref={iframeRef}
              src={previewUrl}
              className="flex-1 border border-gray-200 dark:border-gray-700 rounded-lg"
              title="WebContainer React Demo"
            />
          )}

          {!previewUrl && !isLoading && (
            <div className="flex-1 flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="text-center">
                <Title level={4} type="secondary">点击"启动演示"开始</Title>
                <Text type="secondary">
                  使用真正的 @webcontainer/api 来创建完整的开发环境
                </Text>
              </div>
            </div>
          )}
        </div>

        {/* 日志面板 */}
        <div className="w-80 flex flex-col">
          <Card title="运行日志" className="flex-1">
            <div className="h-64 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-2 rounded text-sm font-mono">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
